Feature: User Account Management
    As a user of the QBank platform
    I want to manage my account, authentication, and roles
    So that I can access platform features securely and efficiently

    Background:
        Given the QBank platform is running
        And the notification service is available
        And the email service is configured

    @registration @email-verification
    Scenario: User registers with email and password
        Given I am on the registration page
        When I fill in the registration form with:
            | field            | value               |
            | email            | <EMAIL> |
            | password         | SecurePass123!      |
            | confirm_password | SecurePass123!      |
            | name             | <PERSON>            |
        And I submit the registration form
        Then I should see a message "Registration successful! Please check your email to verify your account."
        And a verification email should be sent to "<EMAIL>"
        And the user account should be created with status "unverified"
        And I should be redirected to the login page

    @registration @email-verification
    Scenario: User verifies email address
        Given a user "<EMAIL>" has registered but not verified their email
        And a verification email was sent to "<EMAIL>"
        When I click the verification link in the email
        Then I should see a message "Email verified successfully!"
        And the user account status should be updated to "verified"
        And I should be redirected to the profile setup page

    @registration @validation
    Scenario: Registration with duplicate email
        Given a user with email "<EMAIL>" already exists
        When I try to register with email "<EMAIL>"
        Then I should see an error "An account with this email already exists"
        And no new user account should be created

    @registration @validation
    Scenario: Registration with invalid password
        Given I am on the registration page
        When I fill in the registration form with:
            | field            | value               |
            | email            | <EMAIL> |
            | password         | weak                |
            | confirm_password | weak                |
            | name             | John Doe            |
        And I submit the registration form
        Then I should see password validation errors
        And no user account should be created

    @oauth @registration
    Scenario: User registers with Google OAuth
        Given I am on the registration page
        When I click "Sign up with Google"
        And I complete the Google OAuth flow with:
            | email | <EMAIL> |
            | name  | Google User          |
        Then a user account should be created with status "verified"
        And I should be redirected to the profile setup page
        And no verification email should be sent

    @login @authentication
    Scenario: User logs in with valid credentials
        Given a verified user exists with:
            | email    | <EMAIL> |
            | password | SecurePass123!   |
        When I go to the login page
        And I enter email "<EMAIL>" and password "SecurePass123!"
        And I click "Login"
        Then I should be logged in successfully
        And I should see the dashboard
        And a session should be established

    @login @authentication @unverified
    Scenario: Unverified user attempts to login
        Given an unverified user exists with:
            | email    | <EMAIL> |
            | password | SecurePass123!         |
        When I go to the login page
        And I enter email "<EMAIL>" and password "SecurePass123!"
        And I click "Login"
        Then I should be logged in with limited access
        And I should see a message "Please verify your email to access all features"
        And I should see a "Resend verification email" button

    @login @authentication @failed
    Scenario: User login with invalid credentials
        Given a verified user exists with email "<EMAIL>"
        When I go to the login page
        And I enter email "<EMAIL>" and password "wrongpassword"
        And I click "Login"
        Then I should see an error "Invalid email or password"
        And I should remain on the login page
        And no session should be established

    @login @security @account-lockout
    Scenario: Account lockout after multiple failed login attempts
        Given a verified user exists with email "<EMAIL>"
        When I attempt to login with wrong password 5 times for "<EMAIL>"
        Then the account should be temporarily locked
        And I should see an error "Account temporarily locked due to multiple failed login attempts"
        And I should see a captcha challenge on subsequent login attempts

    @oauth @login
    Scenario: User logs in with Google OAuth
        Given a user registered with Google OAuth exists with email "<EMAIL>"
        When I go to the login page
        And I click "Sign in with Google"
        And I complete the Google OAuth flow
        Then I should be logged in successfully
        And I should see the dashboard

    @profile @setup
    Scenario: New user completes profile setup
        Given I am a newly verified user logged in for the first time
        When I am on the profile setup page
        And I fill in the mandatory profile fields:
            | field          | value                |
            | exam_target    | JEE Main             |
            | interests      | Physics, Mathematics |
            | study_level    | 12th Grade           |
            | preferred_lang | English              |
        And I submit the profile setup form
        Then my profile should be marked as complete
        And I should be redirected to the student dashboard
        And my default role should be set to "student"

    @profile @management
    Scenario: User updates profile information
        Given I am logged in as a verified user with complete profile
        When I go to my profile page
        And I update my profile with:
            | field  | value              |
            | name   | Updated Name       |
            | bio    | I love mathematics |
            | avatar | new_avatar.jpg     |
        And I save the changes
        Then my profile should be updated successfully
        And I should see a confirmation message "Profile updated successfully"
        And the changes should be reflected in my profile

    @profile @auto-save
    Scenario: Profile changes are auto-saved as drafts
        Given I am logged in and editing my profile
        When I make changes to my profile fields
        And I navigate away without saving
        And I return to the profile edit page
        Then my unsaved changes should be restored from draft
        And I should see a message "Draft restored"

    @profile @validation
    Scenario: Profile update with invalid data
        Given I am logged in and editing my profile
        When I try to upload an invalid avatar file format
        Then I should see an error "Unsupported file format. Please use JPG, PNG, or GIF."
        And the avatar should not be updated
        And my other profile changes should be preserved

    @role-toggle @student-creator
    Scenario: User switches from Student to Creator mode
        Given I am logged in as a student with complete profile
        And I am currently in "student" mode
        When I click "Switch to Creator" in the navigation
        Then I should be switched to "creator" mode
        And I should see the creator dashboard
        And I should see creator-specific features like:
            | feature            |
            | MCQ creation tools |
            | Follower count     |
            | Earnings dashboard |
            | Content analytics  |
        And my session should be updated with creator context

    @role-toggle @creator-student
    Scenario: User switches from Creator to Student mode
        Given I am logged in as a creator with complete profile
        And I am currently in "creator" mode
        When I click "Switch to Student" in the navigation
        Then I should be switched to "student" mode
        And I should see the student dashboard
        And I should see student-specific features like:
            | feature             |
            | Practice feed       |
            | Available tests     |
            | Learning statistics |
            | Progress tracking   |
        And my session should be updated with student context

    @role-toggle @confirmation
    Scenario: User confirms role switch with unsaved changes
        Given I am logged in as a student
        And I am in the middle of creating content with unsaved changes
        When I click "Switch to Creator"
        Then I should see a confirmation dialog "You have unsaved changes. Are you sure you want to switch modes?"
        When I click "Confirm"
        Then I should be switched to "creator" mode
        And my unsaved changes should be lost
        And I should see the creator dashboard

    @role-toggle @cancel
    Scenario: User cancels role switch to preserve unsaved changes
        Given I am logged in as a student
        And I am in the middle of creating content with unsaved changes
        When I click "Switch to Creator"
        And I see a confirmation dialog about unsaved changes
        When I click "Cancel"
        Then I should remain in "student" mode
        And my unsaved changes should be preserved
        And I should stay on the current page

    @roles @moderator
    Scenario: Moderator accesses moderation features
        Given I am logged in as a moderator
        When I access the platform
        Then I should see only moderation-specific interface
        And I should have access to:
            | feature                  |
            | Content moderation queue |
            | User report reviews      |
            | Content approval actions |
            | Moderation dashboard     |
            | User management tools    |
        And I should not see student or creator features
        And I should not have access to role toggle options

    @roles @moderator @restrictions
    Scenario: Moderator cannot access student/creator features
        Given I am logged in as a moderator
        When I try to access student features like practice tests
        Then I should see an error "Access denied. Moderators cannot access student features."
        When I try to access creator features like MCQ creation
        Then I should see an error "Access denied. Moderators cannot access creator features."
        And I should be redirected to the moderation dashboard

    @roles @moderator @registration
    Scenario: Moderator account is created by admin
        Given I am a superuser admin
        When I create a new moderator account with:
            | field    | value                 |
            | email    | <EMAIL> |
            | username | mod_reviewer          |
            | name     | Jane Moderator        |
            | role     | moderator             |
        Then a moderator-only account should be created
        And the account should not have student or creator capabilities
        And the moderator should receive login credentials via email
        And the account should be marked as "moderator_only"

    @roles @moderator @email-uniqueness
    Scenario: Cannot create regular user account with moderator email
        Given a moderator account exists with email "<EMAIL>"
        When someone tries to register as a regular user with email "<EMAIL>"
        Then the registration should fail
        And I should see an error "This email is already associated with a moderator account"
        And no regular user account should be created

    @roles @moderator @username-uniqueness
    Scenario: Cannot create regular user account with moderator username
        Given a moderator account exists with username "mod_reviewer"
        When someone tries to register as a regular user with username "mod_reviewer"
        Then the registration should fail
        And I should see an error "This username is already taken by a moderator account"
        And no regular user account should be created

    @roles @moderator @role-conversion-prevention
    Scenario: Cannot convert regular user to moderator
        Given I am a regular user with email "<EMAIL>"
        And I am logged in as a superuser admin
        When I try to change the user's role to "moderator"
        Then the role change should fail
        And I should see an error "Cannot convert existing user accounts to moderator role. Create a new moderator account instead."
        And the user should remain a regular user

    @roles @moderator @profile-restrictions
    Scenario: Moderator has limited profile options
        Given I am logged in as a moderator
        When I access my profile settings
        Then I should only see moderator-relevant profile fields:
            | field                  |
            | Name                   |
            | Contact information    |
            | Moderation preferences |
            | Notification settings  |
        And I should not see fields like:
            | field                |
            | Exam target          |
            | Study interests      |
            | Creator bio          |
            | Follower preferences |

    @roles @superuser
    Scenario: Superuser accesses admin features
        Given I am logged in as a superuser
        When I access the Django admin interface
        Then I should have full administrative access
        And I should be able to:
            | action                       |
            | Manage all user accounts     |
            | Bulk upload MCQs via CSV     |
            | Create and manage tests      |
            | Override system restrictions |
            | Access all platform data     |

    @subscription @free-tier
    Scenario: Free tier user has limited access
        Given I am logged in as a free tier user
        When I access platform features
        Then I should have access to basic features
        But I should see limitations like:
            | limitation                     |
            | Daily question practice limit  |
            | Basic analytics only           |
            | Advertisements displayed       |
            | Limited creator content access |
        And I should see upgrade prompts for premium features

    @subscription @premium-upgrade
    Scenario: User upgrades to Premium subscription
        Given I am logged in as a free tier user
        When I choose a Premium plan
        And I complete the payment process successfully
        Then my account should be upgraded to Premium
        And I should have access to premium features:
            | feature                           |
            | Unlimited question practice       |
            | Detailed analytics and reports    |
            | Ad-free experience                |
            | Access to premium creator content |
            | Priority customer support         |
        And I should see a confirmation "Welcome to Premium!"

    @subscription @payment-failure
    Scenario: Premium upgrade fails due to payment issues
        Given I am logged in as a free tier user
        When I choose a Premium plan
        And the payment process fails
        Then my account should remain as free tier
        And I should see an error "Payment failed. Please try again or use a different payment method."
        And I should be redirected back to the subscription page

    @subscription @expiration
    Scenario: Premium subscription expires
        Given I am a premium user with an expiring subscription
        When my subscription expires
        Then my account should revert to free tier
        And I should lose access to premium features
        And I should see a notification "Your premium subscription has expired"
        And I should see options to renew my subscription

    @notifications @preferences
    Scenario: User manages notification preferences
        Given I am logged in with a complete profile
        When I go to notification settings
        And I update my preferences:
            | notification_type | email | push  | in_app |
            | New followers     | true  | true  | true   |
            | Content approved  | true  | false | true   |
            | System updates    | false | false | true   |
            | Marketing emails  | false | false | false  |
        And I save the preferences
        Then my notification settings should be updated
        And I should only receive notifications according to my preferences

    @password-reset @security
    Scenario: User requests password reset
        Given a verified user exists with email "<EMAIL>"
        When I go to the login page
        And I click "Forgot Password?"
        And I enter email "<EMAIL>"
        And I submit the password reset request
        Then I should see a message "Password reset link sent to your email"
        And a password reset email should be sent to "<EMAIL>"
        And the reset link should be valid for 24 hours

    @password-reset @security
    Scenario: User resets password using valid link
        Given a user has requested a password reset
        And a valid reset link was sent to their email
        When I click the password reset link
        And I enter a new password "NewSecurePass123!"
        And I confirm the new password "NewSecurePass123!"
        And I submit the password reset form
        Then my password should be updated successfully
        And I should see a message "Password reset successful"
        And I should be redirected to the login page
        And the reset link should be invalidated

    @password-reset @security @expired
    Scenario: User tries to use expired password reset link
        Given a user has requested a password reset 25 hours ago
        When I click the expired password reset link
        Then I should see an error "This password reset link has expired"
        And I should be redirected to the password reset request page
        And I should see an option to request a new reset link

    @email-verification @resend
    Scenario: User requests new verification email
        Given an unverified user is logged in with limited access
        When I click "Resend verification email"
        Then a new verification email should be sent
        And I should see a message "Verification email sent successfully"
        And the previous verification link should be invalidated

    @email-verification @expired
    Scenario: User tries to use expired verification link
        Given a user has an expired verification link
        When I click the expired verification link
        Then I should see an error "This verification link has expired"
        And I should be redirected to request a new verification email
        And my account should remain unverified

    @social-connections @linking
    Scenario: User links social account to existing account
        Given I am logged in with email/password authentication
        When I go to account settings
        And I click "Link Google Account"
        And I complete the Google OAuth flow
        Then my Google account should be linked to my existing account
        And I should be able to login using either method
        And I should see "Google account linked successfully"

    @social-connections @unlinking
    Scenario: User unlinks social account
        Given I am logged in with both email/password and Google OAuth linked
        When I go to account settings
        And I click "Unlink Google Account"
        And I confirm the unlinking
        Then my Google account should be unlinked
        And I should only be able to login with email/password
        And I should see "Google account unlinked successfully"

    @account-deletion @privacy
    Scenario: User requests account deletion
        Given I am logged in with a complete profile
        When I go to account settings
        And I click "Delete Account"
        And I confirm account deletion by entering my password
        And I acknowledge the data deletion warning
        Then my account should be scheduled for deletion
        And I should receive a confirmation email
        And I should be logged out immediately
        And I should see "Account deletion initiated. You have 30 days to cancel."

    @account-deletion @cancellation
    Scenario: User cancels account deletion within grace period
        Given my account is scheduled for deletion
        And I am within the 30-day grace period
        When I click the cancellation link in the deletion email
        And I login to my account
        Then my account deletion should be cancelled
        And my account should be fully restored
        And I should see "Account deletion cancelled successfully"

    @security @session-management
    Scenario: User logs out from all devices
        Given I am logged in on multiple devices
        When I go to security settings
        And I click "Log out from all devices"
        And I confirm the action
        Then all my active sessions should be terminated
        And I should be logged out from all devices
        And I should see "Logged out from all devices successfully"

    @security @concurrent-sessions
    Scenario: User manages concurrent login sessions
        Given I am logged in on multiple devices
        When I go to security settings
        And I view "Active Sessions"
        Then I should see a list of all active sessions with:
            | information     |
            | Device type     |
            | Location        |
            | Last activity   |
            | Current session |
        And I should be able to terminate individual sessions

    @following @creator-student
    Scenario: Student follows a creator
        Given I am logged in as a student
        And a creator "math_expert" exists
        When I visit the creator's profile
        And I click "Follow"
        Then I should be following "math_expert"
        And the creator should receive a notification about the new follower
        And the creator's follower count should increase by 1
        And I should see "Following" button instead of "Follow"

    @following @unfollow
    Scenario: Student unfollows a creator
        Given I am logged in as a student
        And I am following creator "math_expert"
        When I visit the creator's profile
        And I click "Unfollow"
        Then I should no longer be following "math_expert"
        And the creator's follower count should decrease by 1
        And I should see "Follow" button instead of "Following"

    @following @self-follow-prevention
    Scenario: User cannot follow themselves
        Given I am logged in as a user who is both student and creator
        When I switch to student mode
        And I try to visit my own creator profile
        Then I should not see a "Follow" button
        And I should see "This is your profile" message

    @data-privacy @gdpr
    Scenario: User requests data export
        Given I am logged in with a complete profile and activity history
        When I go to privacy settings
        And I click "Download My Data"
        And I confirm the data export request
        Then I should receive an email when the export is ready
        And the export should include all my personal data in JSON format
        And I should see "Data export initiated. You'll receive an email when ready."

    @performance @high-load
    Scenario: System handles high concurrent login load
        Given the platform is experiencing high traffic
        When multiple users attempt to login simultaneously
        Then the authentication system should remain responsive
        And login requests should be processed within acceptable time limits
        And users should not experience authentication failures due to load

    @institutional @bulk-accounts
    Scenario: Institution admin manages bulk user accounts
        Given I am logged in as an institutional admin
        When I upload a CSV file with student account details
        And the CSV contains valid user information
        Then bulk user accounts should be created successfully
        And verification emails should be sent to all new users
        And I should see a summary report of the account creation process
        And any errors should be clearly reported